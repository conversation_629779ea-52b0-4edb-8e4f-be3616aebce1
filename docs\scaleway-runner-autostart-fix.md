# Scaleway Mac GitHub Actions Runner Auto-Start Fix

## Problem
The GitHub Actions runner on Scaleway Mac mini does not auto-start on boot because it's configured as a LaunchAgent (user-level service) instead of a LaunchDaemon (system-level service).

## Solution
Convert the runner from LaunchAgent to LaunchDaemon configuration to enable automatic startup at boot.

## Implementation Steps

### 1. Uninstall Existing Service
```bash
# Navigate to runner directory
cd ~/actions-runner

# Uninstall existing LaunchAgent service
sudo ./svc.sh uninstall
```

### 2. Modify svc.sh Configuration
Edit the `svc.sh` file to use LaunchDaemons instead of LaunchAgents:

```bash
# Create a backup of the original file
cp svc.sh svc.sh.backup

# Open svc.sh for editing
nano svc.sh
```

**Required Changes:**

1. **Remove the sudo check** (lines 9-12):
   ```bash
   # DELETE these lines:
   if [ $user_id -eq 0 ]; then
       echo "Must not run with sudo"
       exit 1
   fi
   ```

2. **Change LaunchAgents to LaunchDaemons** (line 18):
   ```bash
   # CHANGE this line:
   LAUNCH_PATH="${HOME}/Library/LaunchAgents"
   # TO:
   LAUNCH_PATH="/Library/LaunchDaemons"
   ```

3. **Update log path** (line 58):
   ```bash
   # CHANGE this line:
   log_path="${HOME}/Library/Logs/${SVC_NAME}"
   # TO:
   log_path="/var/log/${SVC_NAME}"
   ```

4. **Remove UserHome reference** (line 62):
   ```bash
   # CHANGE this line:
   sed "s/{{User}}/${USER:-$SUDO_USER}/g; s/{{SvcName}}/$SVC_NAME/g; s@{{RunnerRoot}}@${RUNNER_ROOT}@g; s@{{UserHome}}@$HOME@g;" "${TEMPLATE_PATH}" > "${TEMP_PATH}" || failed "failed to create replacement temp file"
   # TO:
   sed "s/{{User}}/${USER:-$SUDO_USER}/g; s/{{SvcName}}/$SVC_NAME/g; s@{{RunnerRoot}}@${RUNNER_ROOT}@g; s@{{UserHome}}@${RUNNER_ROOT}@g;" "${TEMPLATE_PATH}" > "${TEMP_PATH}" || failed "failed to create replacement temp file"
   ```

### 3. Set Proper Ownership
```bash
# Change ownership of runsvh.sh to root
sudo chown root runsvh.sh
```

### 4. Install as LaunchDaemon
```bash
# Install the service as a LaunchDaemon
sudo ./svc.sh install

# Start the service
sudo ./svc.sh start
```

### 5. Verify Installation
```bash
# Check if service is running
sudo launchctl list | grep runner

# Verify the process is running as root (replace PID with actual PID from previous command)
sudo ps aux | grep [PID_FROM_LAUNCHCTL]
```

### 6. Configure Power Management
To ensure the runner works properly, configure macOS power settings:

**System Preferences > Energy Saver:**
- Disable "Turn display off after" or set to "Never"
- Disable "Put hard disks to sleep when possible"
- Disable "Wake for network access" (optional)
- Ensure "Prevent computer from sleeping automatically when the display is off" is checked

**System Preferences > Screen Saver:**
- Set to "Never" or a very long time

### 7. Test Auto-Start
```bash
# Reboot the system to test auto-start
sudo reboot
```

After reboot, verify the runner is automatically running:
```bash
# Check service status
sudo launchctl list | grep runner

# Verify runner is connected to GitHub
# Check runner logs or GitHub repository settings
```

## Troubleshooting

### Service Not Starting
- Verify LaunchDaemon plist file exists in `/Library/LaunchDaemons/`
- Check file permissions and ownership
- Review system logs: `sudo log show --predicate 'subsystem == "com.apple.launchd"'`

### Runner Not Connecting
- Verify network connectivity
- Check runner token validity
- Review runner logs in the runner directory

### Permission Issues
- Ensure all runner files have proper ownership
- Verify the service runs with root privileges
- Check that the runner directory is accessible by the daemon

## Notes
- This configuration requires the runner to operate as a system service
- The runner will start automatically on every boot without requiring user login
- Power management settings are crucial for headless operation
- Regular monitoring of runner status is recommended

## References
- Based on community solution from GitHub Actions runner issues
- Tested on macOS with Scaleway Mac mini infrastructure
